import {KeyValue} from "@angular/common";
import {Component, Input, OnInit} from "@angular/core";
import {BaseDtoIfc} from "@lib/common/interfaces";
import {isEqual} from "lodash";
import {combineLatest, distinctUntilChanged, map, Observable, shareReplay, take, takeUntil} from "rxjs";
import {BulkEditCellComponent} from "./bulk.edit.cell.component";
import {TBulkEditCellValue} from "./bulk.edit.cell.types";


@Component({template: ''}) 
export abstract class BulkEditSelectCellComponent<IFC extends BaseDtoIfc> extends BulkEditCellComponent<IFC> implements OnInit {
    
    /*******************************************************************************************************************
    * Inputs
    *******************************************************************************************************************/
    @Input() public options?: Array<KeyValue<string, string>>;

    /*******************************************************************************************************************
    * Protected
    *******************************************************************************************************************/
    protected options$!: Observable<Array<KeyValue<string, string>>>;
    
    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public override ngOnInit (): void {
        super.ngOnInit();
        this.options$ = this.buildOptions$(this.rows$);
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected buildOptions$(rows$: Observable<Array<IFC>>): Observable<Array<KeyValue<string, string>>> {
        return rows$.pipe(
            takeUntil(this.destroy$),
            map((rows) => this.options 
                ? ((typeof this.defaultVal !== undefined) 
                    ? [{key: this.defaultVal, value: this.defaultVal}, ...(this.options)] 
                    : this.options
                ) : this.mapOptions(rows)
            ),
            distinctUntilChanged(isEqual),
            shareReplay({bufferSize: 1, refCount: true})
        );
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected override buildEditable$(rows$: Observable<Array<IFC>>): Observable<boolean> {
        const editable$ = super.buildEditable$(rows$);
        const options$  = this.options$ ?? this.buildOptions$(rows$);

        return combineLatest([editable$, options$])
            .pipe(
                takeUntil(this.destroy$),
                map(([editable, options]) => editable && ((options?.length ?? 0) > 1)),
                distinctUntilChanged(),
                shareReplay({bufferSize: 1, refCount: true})
            );
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected override onModelChange(value: TBulkEditCellValue): void {
        console.log('onModelChange', value);
        // if (value === this.defaultVal) return;

        combineLatest([this.value$, this.rows$])
            .pipe(take(1))
            .subscribe(([prev, rows]) => {
                if (prev === value) return;
                const compressed = this.compressTransformations(this.transform(value, rows));
                (compressed.length) && this.patch(compressed);
                (compressed.length) && this.value$.next(value);
            });
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected mapOptions(_selection: Array<IFC>): Array<KeyValue<string, string>> {
        return [];
    }
}