import {Component, inject, Input, OnD<PERSON>roy, OnInit} from "@angular/core";
import {DEFAULT_ALL_GROUP_LABEL} from "@lib/common/enums/group.enums";
import {ROLE_ACTIONS, ROLE_SUBJECTS} from "@lib/common/enums/role.enums";
import {BaseDtoIfc} from "@lib/common/interfaces";
import {ClientAuthorizationService} from "@lib/frontend/core/authorization/client.authorizations.service";
import {ICS_CHANNELS, ICS_TOPICS} from "@lib/frontend/core/interfaces/ics.interfaces";
import {ICService} from "@lib/frontend/core/services/ics/inter.comm.service";
import {CoreDataSource} from "@lib/frontend/shared/datasource/core.data.source";
import {get, isEqual} from "lodash";
import {BehaviorSubject, distinctUntilChanged, map, Observable, shareReplay, Subject, take, takeUntil} from "rxjs";
import {MAX_ENTRIES_PER_UPDATE, TBulkEditCellValue, TransformResult} from "./bulk.edit.cell.types";


@Component({template: ''}) 
export abstract class BulkEditCellComponent<IFC extends BaseDtoIfc> implements OnInit, OnDestroy {
    /*******************************************************************************************************************
    * Inputs
    *******************************************************************************************************************/
    @Input() public roleSubject   !: ROLE_SUBJECTS;
    @Input() public icsChannel    !: ICS_CHANNELS;
    @Input() public datasource    !: CoreDataSource<IFC>;
    @Input() public defaultVal    ?: TBulkEditCellValue;
    @Input() public path = '';
    
    /*******************************************************************************************************************
     * Protected
    *******************************************************************************************************************/
    protected rows$         !: Observable<Array<IFC>>;
    protected value$         : BehaviorSubject<TBulkEditCellValue> = new BehaviorSubject<TBulkEditCellValue>(this.defaultVal ?? null);
    protected editable$     !: Observable<boolean>;
    protected allowedGroups !: Array<string>;
   
    protected destroy$               = new Subject<void>();
    protected readonly ENTRY_LIMIT   = MAX_ENTRIES_PER_UPDATE;
    protected readonly roleAction    = ROLE_ACTIONS.EDIT;
    protected readonly ics           = inject(ICService);
    protected readonly authorization = inject(ClientAuthorizationService);

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected abstract isRowEditable(row: IFC): boolean;

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected abstract transform(value: TBulkEditCellValue, rows: Array<IFC>): Array<TransformResult>;

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public ngOnInit (): void {
         this.authorization
            .getAllowedGroupLabels(this.roleSubject, this.roleAction)
            .pipe(take(1))
            .subscribe((groups) => {
                this.allowedGroups  = groups;
                this.rows$          = this.buildRows$();
                this.buildValue$(this.rows$).subscribe(this.value$.next.bind(this.value$));
                this.editable$      = this.buildEditable$(this.rows$);
            });
    }
    
    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    public ngOnDestroy(): void { 
        this.destroy$.next();
        this.destroy$.complete();  
        this.value$.complete();
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected onModelChange(value: TBulkEditCellValue): void {
        this.rows$.pipe(take(1)).subscribe((rows) => {
            const compressed = this.compressTransformations(this.transform(value, rows));
            (compressed.length) && this.patch(compressed);
        });
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected buildRows$(): Observable<Array<IFC>> {
        return this.datasource?.selected.pipe(
            takeUntil(this.destroy$),
            distinctUntilChanged(isEqual),
            map(this.mapRows.bind(this)),
            shareReplay({bufferSize: 1, refCount: true})
        );
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected buildEditable$(rows$: Observable<Array<IFC>>): Observable<boolean> {
        return rows$.pipe(
            takeUntil(this.destroy$),
            map(this._checkSelectionLimit.bind(this)),
            distinctUntilChanged(),
            shareReplay({bufferSize: 1, refCount: true})
        );
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected buildValue$(rows$: Observable<Array<IFC>>): Observable<TBulkEditCellValue> {
        return rows$.pipe(
            takeUntil(this.destroy$),
            map((rows) => this.mapValue(rows)),
            distinctUntilChanged(isEqual),
            shareReplay({bufferSize: 1, refCount: true})
        );
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected mapRows(selection: Array<string>): Array<IFC> {
        return selection.reduce((acc, curr) => {
            const row = this.datasource.data.find((r) => r.uuid === curr);
            row && acc.push(row);
            return acc;
        }, <Array<IFC>>[]);
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected mapEditable(selection: Array<IFC>): boolean {
        const {allowedGroups} = this;

        if (!(allowedGroups?.length && selection.length   )) { return false; }
        if (allowedGroups.includes(DEFAULT_ALL_GROUP_LABEL)) { return selection.every(this.isRowEditable.bind(this)); }
        
        for (const row of selection) {
            const {groups} = row;
            
            if (!(groups?.length && groups.some((g) => allowedGroups.includes(g)))) { return false; }
            if (!this.isRowEditable(row)) { return false; }
        }

        return true;
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected mapValue(selection: Array<IFC>): TBulkEditCellValue {
        return (
            (selection.length === 1) || 
            ((new Set(selection.map((s) => get(s, this.path))).size === 1) && (selection.length > 1))
        ) ? get(selection.at(0), this.path, this.defaultVal) : (this.defaultVal);

    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected patch(entries: Array<TransformResult>): void {
        this.ics.channel(this.icsChannel)
            .request(ICS_TOPICS.CACHE_ITEM_PATCH_BULK_REQUEST, entries)
            .pipe(take(1))
            .subscribe();
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    protected compressTransformations(transformations: Array<TransformResult>): Array<TransformResult> {
        const compressed: Record<string, TransformResult> = {};

        for (const item of transformations) {
            const key = item.uuid;
            if (!compressed[key]) {
                compressed[key] = { uuid: key };
            }
            compressed[key] = { ...compressed[key], ...item };
        }

        return Object.values(compressed);
    }

    /*******************************************************************************************************************
    *
    *******************************************************************************************************************/
    private _checkSelectionLimit(selection: Array<IFC>): boolean {
        return (selection.length && selection.length <= this.ENTRY_LIMIT) 
            ? this.mapEditable(selection)
            : false;
    }
}